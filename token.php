<?php
header("Content-Type: text/plain");

if (file_exists('sessionId')) {
    $json = json_decode(file_get_contents('sessionId'), true);
    if (time() <= $json['exp_date']) {
        echo $json['sessionId'];
    } else {
        get_cookie_login();
        $json = json_decode(file_get_contents('sessionId'), true);
        echo $json['sessionId'];
    }
} else {
    get_cookie_login();
    $json = json_decode(file_get_contents('sessionId'), true);
    echo $json['sessionId'];
}

function get_cookie_login() {

    $date = time();
    $exp_date = strtotime("+1 hour", $date);

    $ch = curl_init();
    $headers = array
    (
        'host: 836.norago.tv',
'accept: application/json',
'charset: UTF-8',
'cookie: ',
'user-agent: Dalvik/2.1.0 (Linux; U; Android 14; Pixel Fold Build/AP2A.240805.005) 4.9.2 felix/Pixel Fold(Google,felix,google) Android/34(REL) NoraGO/soplayer(2.9.230.618)',
'content-type: application/json; charset=UTF-8',

    );
curl_setopt($ch, CURLOPT_HEADER, true); // Incluye los headers en la salida
curl_setopt($ch, CURLOPT_NOBODY, false); // Asegura que el cuerpo de la respuesta también sea incluido
    curl_setopt($ch, CURLOPT_URL, "https://836.norago.tv/wbs/android/auth");
    curl_setopt($ch, CURLOPT_POSTFIELDS, '{"authCode":"9755653","macAddress":"200060a8814c","model":"Pixel Fold","providerId":"836","serialNumber":"B3377C95E38C21E3B65290A3EF6E45EC"}');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    $response = curl_exec($ch);
    curl_close($ch);
preg_match_all("/JSESSIONID=(.*?);/" , $response , $session);
    $json = json_decode($response, true);
    $session = base64_encode ($session[1][0]);
    $file = '{"sessionId" : "'.$session.'" , "exp_date" : "'.$exp_date.'"}';
    file_put_contents('sessionId', $file);
}