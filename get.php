<?php
error_reporting(0);
header("Content-Type: text/plain");

if (isset($_GET['ts'])) {
    header('Content-Type: video/MP2T');
    echo file_get_contents(hex2bin($_GET['ts']));
    exit();
    } 
    
$id = $_GET["id"];

if (!isset($id)){
	echo("Missing parameter ID");
	exit();
	}

#session_start();
if(is_null($_SESSION['lux'.$id])){
	if(is_null($_SESSION['expire'.$id])){
		$url = str_replace("\"" , "" , get_data('https://836.norago.tv/wbs/api/media/channels/'.$id));
		header("Location: ".str_replace("\"" , "" , $url)); exit;


		$path = str_replace(end(explode("/" , $url)) , NULL , $url);
		$row = explode(PHP_EOL , file_get_contents($url));
		array_pop($row);
		$_SESSION['lux'.$id] = $path.trim(end($row));
		$_SESSION['expire'.$id] = strtotime("+30 minute", time());
		}
	}

if(time() <= $_SESSION['expire'.$id] ){
	$path = str_replace(end(explode("/" , $_SESSION['lux'.$id])) , NULL , $_SESSION['lux'.$id]);
	$content = str_replace(",".PHP_EOL , ",".PHP_EOL.$path , file_get_contents($_SESSION['lux'.$id]));
	$part = explode(PHP_EOL , $content);
	for ($i = 0; $i < count($part) - 1; $i++) {
		if (strpos($part[$i], '#') !== false) {
			echo $part[$i].PHP_EOL;
			}else{
				echo  'http://'.$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF'].'?ts='.bin2hex($part[$i]).PHP_EOL;
				}
			}
	}else{
		$url = str_replace("\"" , "" , get_data('https://836.norago.tv/wbs/api/media/channels/'.$id));
		$path = str_replace(end(explode("/" , $url)) , NULL , $url);
		$row = explode(PHP_EOL , file_get_contents($url));
		array_pop($row);
		$_SESSION['lux'.$id] = $path.trim(end($row));
		$_SESSION['expire'.$id] = strtotime("+30 minute", time());
		$path = str_replace(end(explode("/" , $_SESSION['lux'.$id])) , NULL , $_SESSION['lux'.$id]);
		$content = str_replace(",".PHP_EOL , ",".PHP_EOL.$path , file_get_contents($_SESSION['lux'.$id]));
		$part = explode(PHP_EOL , $content);
		for ($i = 0; $i < count($part) - 1; $i++) {
			if (strpos($part[$i], '#') !== false) {
				echo $part[$i].PHP_EOL;
				}else{
					echo  'http://'.$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF'].'?ts='.bin2hex($part[$i]).PHP_EOL;
					}
				}
		}
		
		
		
		
		
		
		
		
		
	

function get_data($url){
	$cookie =  @file(str_replace("get.php",'token.php',"http://{$_SERVER['HTTP_HOST']}{$_SERVER['PHP_SELF']}")); 
	$ch =curl_init();
	curl_setopt($ch, CURLOPT_URL, $url );
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_HTTPHEADER,
	array
	(
	'host: 836.norago.tv',
'accept: application/json',
'charset: UTF-8',
'user-agent: Dalvik/2.1.0 (Linux; U; Android 14; Pixel Fold Build/AP2A.240805.005) 4.9.2 felix/Pixel Fold(Google,felix,google) Android/34(REL) NoraGO/soplayer(2.9.230.618)',
	'cookie: JSESSIONID='.base64_decode($cookie[0]).';'
	));
	$content = curl_exec($ch);
	curl_close($ch);
	return $content;
	}
