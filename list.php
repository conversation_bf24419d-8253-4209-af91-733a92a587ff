<?php
error_reporting(0);
header("Content-Type: text/plain");
header('Content-Type: charset=utf-8');
$server = str_replace("list.php" , "get.php" , "http://".$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF']);

$json = json_decode(get_data('https://836.norago.tv/wbs/api/media/channels/?categoryId=0&type=TV') , true);
$categories = json_decode(get_data('https://836.norago.tv/wbs/api/media/tv/categories') , true);
for ($i=0; $i < count($categories); $i++) {
	$tv_categorie[$categories[$i]["id"]] = array("name" => $categories[$i]["name"]);
	}
$tv_cat_json  = json_decode(json_encode($tv_categorie) , true);

echo "#EXTM3U".PHP_EOL;
foreach($json as $i => $item){
	if($item["categoryIds"][0] == null){
		$tv_cat_json[$item["categoryIds"][0]]["name"] = "NO GROUP";
		}
	echo '#EXTINF:-1 group-title="'.strtoupper($tv_cat_json[$item["categoryIds"][0]]["name"]).'" tvg-id="'.$item["epgId"].'" tvg-logo="'.str_replace("{{size}}" , "50x50" , $item["logoUrl"]).'",'.$item["name"].PHP_EOL;
	echo $server."?id=".$item["id"]."&.m3u8".PHP_EOL.PHP_EOL;
	}

function get_data($url){
	$cookie =  @file(str_replace("list.php",'token.php',"http://{$_SERVER['HTTP_HOST']}{$_SERVER['PHP_SELF']}"));   
	$ch =curl_init();
	curl_setopt($ch, CURLOPT_URL, $url );
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_HTTPHEADER,
	
	array
	(
	'host: 836.norago.tv',
'accept: application/json',
'charset: UTF-8',
'user-agent: Dalvik/2.1.0 (Linux; U; Android 14; Pixel Fold Build/AP2A.240805.005) 4.9.2 felix/Pixel Fold(Google,felix,google) Android/34(REL) NoraGO/soplayer(2.9.230.618)',
	'cookie: JSESSIONID='.base64_decode($cookie[0]).';'
	));
	$content = curl_exec($ch);
	curl_close($ch);
	return $content;
	}
